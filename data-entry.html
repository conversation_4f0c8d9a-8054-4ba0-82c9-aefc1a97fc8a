<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据录入 - 医疗CRF编辑器</title>
    <script src="https://cdn.jsdelivr.net/npm/@unocss/runtime"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
        
        body {
            font-family: 'Inter', sans-serif;
            background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
            min-height: 100vh;
        }
        
        .glass-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }
        
        .form-section {
            transition: all 0.3s ease;
        }
        
        .form-section:hover {
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }
        
        .input-group {
            position: relative;
        }
        
        .input-group input:focus + .input-label,
        .input-group input:not(:placeholder-shown) + .input-label {
            transform: translateY(-24px) scale(0.85);
            color: #3b82f6;
        }
        
        .input-label {
            position: absolute;
            left: 12px;
            top: 12px;
            color: #6b7280;
            transition: all 0.3s ease;
            pointer-events: none;
            background: white;
            padding: 0 4px;
        }
        
        .smart-input {
            border: 2px solid #e5e7eb;
            transition: all 0.3s ease;
        }
        
        .smart-input:focus {
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }
        
        .smart-input.valid {
            border-color: #10b981;
        }
        
        .smart-input.invalid {
            border-color: #ef4444;
        }
        
        .validation-message {
            font-size: 12px;
            margin-top: 4px;
        }
        
        .validation-message.success {
            color: #10b981;
        }
        
        .validation-message.error {
            color: #ef4444;
        }
        
        .progress-indicator {
            background: linear-gradient(90deg, #3b82f6, #1d4ed8);
            height: 4px;
            border-radius: 2px;
            transition: width 0.3s ease;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
            border: none;
            transition: all 0.3s ease;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 16px rgba(59, 130, 246, 0.3);
        }
        
        .patient-card {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            color: white;
        }
        
        .quick-action {
            transition: all 0.3s ease;
            cursor: pointer;
        }
        
        .quick-action:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }
        
        .auto-complete {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background: white;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            z-index: 10;
            max-height: 200px;
            overflow-y: auto;
        }
        
        .auto-complete-item {
            padding: 12px;
            cursor: pointer;
            transition: background-color 0.2s ease;
        }
        
        .auto-complete-item:hover {
            background-color: #f3f4f6;
        }
    </style>
</head>
<body class="m-0 p-0">
    <!-- 导航栏 -->
    <nav class="glass-card fixed top-0 left-0 right-0 z-50 px-6 py-4">
        <div class="flex items-center justify-between max-w-7xl mx-auto">
            <div class="flex items-center space-x-3">
                <i class="fas fa-heartbeat text-3xl text-blue-600"></i>
                <h1 class="text-2xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">MediCRF Pro</h1>
            </div>
            <div class="hidden md:flex items-center space-x-8">
                <a href="index.html" class="text-gray-700 hover:text-blue-600 font-medium transition-colors">首页</a>
                <a href="projects.html" class="text-gray-700 hover:text-blue-600 font-medium transition-colors">项目管理</a>
                <a href="form-designer.html" class="text-gray-700 hover:text-blue-600 font-medium transition-colors">表单设计</a>
                <a href="#" class="text-blue-600 font-medium">数据录入</a>
                <a href="quality-control.html" class="text-gray-700 hover:text-blue-600 font-medium transition-colors">质量控制</a>
            </div>
            <div class="flex items-center space-x-4">
                <button class="p-2 rounded-full hover:bg-gray-100 transition-colors">
                    <i class="fas fa-bell text-gray-600"></i>
                </button>
                <div class="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center">
                    <i class="fas fa-user text-white text-sm"></i>
                </div>
            </div>
        </div>
    </nav>

    <!-- 主内容区域 -->
    <main class="pt-24 pb-12 px-6">
        <div class="max-w-7xl mx-auto">
            <!-- 页面标题和进度 -->
            <div class="mb-8">
                <div class="flex items-center justify-between mb-4">
                    <div>
                        <h2 class="text-3xl font-bold text-gray-800 mb-2">数据录入</h2>
                        <p class="text-gray-600">心血管疾病研究 - 患者基线信息采集</p>
                    </div>
                    <div class="flex items-center space-x-4">
                        <button class="px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors">
                            <i class="fas fa-save mr-2"></i>暂存
                        </button>
                        <button class="btn-primary text-white px-6 py-3 rounded-xl font-semibold">
                            <i class="fas fa-check mr-2"></i>提交
                        </button>
                    </div>
                </div>
                
                <!-- 进度条 -->
                <div class="bg-gray-200 rounded-full h-2 mb-2">
                    <div class="progress-indicator" style="width: 65%"></div>
                </div>
                <div class="flex justify-between text-sm text-gray-600">
                    <span>已完成 65%</span>
                    <span>预计还需 10 分钟</span>
                </div>
            </div>

            <div class="grid grid-cols-1 lg:grid-cols-4 gap-8">
                <!-- 左侧患者信息 -->
                <div class="lg:col-span-1">
                    <div class="patient-card rounded-2xl p-6 mb-6">
                        <div class="flex items-center space-x-4 mb-4">
                            <div class="w-16 h-16 bg-white/20 rounded-full flex items-center justify-center">
                                <i class="fas fa-user text-2xl"></i>
                            </div>
                            <div>
                                <h3 class="text-xl font-semibold">张三</h3>
                                <p class="text-white/80">患者ID: P001234</p>
                            </div>
                        </div>
                        <div class="space-y-2 text-sm">
                            <div class="flex justify-between">
                                <span class="text-white/80">性别:</span>
                                <span>男</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-white/80">年龄:</span>
                                <span>45岁</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-white/80">入组日期:</span>
                                <span>2024-03-15</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-white/80">研究阶段:</span>
                                <span>基线访问</span>
                            </div>
                        </div>
                    </div>

                    <!-- 快速操作 -->
                    <div class="glass-card rounded-2xl p-6">
                        <h4 class="font-semibold text-gray-800 mb-4">快速操作</h4>
                        <div class="space-y-3">
                            <div class="quick-action bg-blue-50 p-3 rounded-lg">
                                <i class="fas fa-copy text-blue-600 mr-2"></i>
                                <span class="text-sm text-blue-700">复制上次访问数据</span>
                            </div>
                            <div class="quick-action bg-green-50 p-3 rounded-lg">
                                <i class="fas fa-magic text-green-600 mr-2"></i>
                                <span class="text-sm text-green-700">智能填充建议</span>
                            </div>
                            <div class="quick-action bg-purple-50 p-3 rounded-lg">
                                <i class="fas fa-history text-purple-600 mr-2"></i>
                                <span class="text-sm text-purple-700">查看历史记录</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 中间表单区域 -->
                <div class="lg:col-span-3">
                    <div class="space-y-8">
                        <!-- 基本信息 -->
                        <div class="form-section glass-card rounded-2xl p-8">
                            <h3 class="text-xl font-semibold text-gray-800 mb-6 flex items-center">
                                <i class="fas fa-user-circle text-blue-600 mr-3"></i>
                                基本信息
                            </h3>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div class="input-group">
                                    <input type="text" class="smart-input w-full px-4 py-3 rounded-xl" placeholder=" " value="张三">
                                    <label class="input-label">患者姓名</label>
                                </div>
                                <div class="input-group">
                                    <input type="text" class="smart-input w-full px-4 py-3 rounded-xl" placeholder=" " value="P001234">
                                    <label class="input-label">患者ID</label>
                                </div>
                                <div class="input-group">
                                    <select class="smart-input w-full px-4 py-3 rounded-xl">
                                        <option value="">请选择</option>
                                        <option value="male" selected>男</option>
                                        <option value="female">女</option>
                                    </select>
                                    <label class="input-label">性别</label>
                                </div>
                                <div class="input-group">
                                    <input type="date" class="smart-input w-full px-4 py-3 rounded-xl" value="1979-03-15">
                                    <label class="input-label">出生日期</label>
                                </div>
                            </div>
                        </div>

                        <!-- 生命体征 -->
                        <div class="form-section glass-card rounded-2xl p-8">
                            <h3 class="text-xl font-semibold text-gray-800 mb-6 flex items-center">
                                <i class="fas fa-heartbeat text-red-600 mr-3"></i>
                                生命体征
                            </h3>
                            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                                <div class="input-group">
                                    <input type="number" class="smart-input w-full px-4 py-3 rounded-xl" placeholder=" " id="systolic">
                                    <label class="input-label">收缩压 (mmHg)</label>
                                    <div class="validation-message" id="systolic-msg"></div>
                                </div>
                                <div class="input-group">
                                    <input type="number" class="smart-input w-full px-4 py-3 rounded-xl" placeholder=" " id="diastolic">
                                    <label class="input-label">舒张压 (mmHg)</label>
                                    <div class="validation-message" id="diastolic-msg"></div>
                                </div>
                                <div class="input-group">
                                    <input type="number" class="smart-input w-full px-4 py-3 rounded-xl" placeholder=" " id="heartRate">
                                    <label class="input-label">心率 (次/分)</label>
                                    <div class="validation-message" id="heartRate-msg"></div>
                                </div>
                                <div class="input-group">
                                    <input type="number" step="0.1" class="smart-input w-full px-4 py-3 rounded-xl" placeholder=" " id="temperature">
                                    <label class="input-label">体温 (°C)</label>
                                    <div class="validation-message" id="temperature-msg"></div>
                                </div>
                            </div>
                        </div>

                        <!-- 用药信息 -->
                        <div class="form-section glass-card rounded-2xl p-8">
                            <h3 class="text-xl font-semibold text-gray-800 mb-6 flex items-center">
                                <i class="fas fa-pills text-green-600 mr-3"></i>
                                当前用药
                            </h3>
                            <div id="medicationList" class="space-y-4">
                                <div class="medication-item border border-gray-200 rounded-xl p-4">
                                    <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                                        <div class="input-group relative">
                                            <input type="text" class="smart-input w-full px-4 py-3 rounded-xl" placeholder=" " id="medication1">
                                            <label class="input-label">药物名称</label>
                                            <div class="auto-complete hidden" id="medication1-complete">
                                                <div class="auto-complete-item">阿司匹林</div>
                                                <div class="auto-complete-item">阿托伐他汀</div>
                                                <div class="auto-complete-item">美托洛尔</div>
                                            </div>
                                        </div>
                                        <div class="input-group">
                                            <input type="text" class="smart-input w-full px-4 py-3 rounded-xl" placeholder=" ">
                                            <label class="input-label">剂量</label>
                                        </div>
                                        <div class="input-group">
                                            <select class="smart-input w-full px-4 py-3 rounded-xl">
                                                <option value="">请选择</option>
                                                <option value="qd">每日一次</option>
                                                <option value="bid">每日两次</option>
                                                <option value="tid">每日三次</option>
                                                <option value="qid">每日四次</option>
                                            </select>
                                            <label class="input-label">用药频次</label>
                                        </div>
                                        <div class="flex items-center">
                                            <button class="text-red-500 hover:text-red-700 p-2">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <button class="mt-4 px-4 py-2 bg-blue-50 text-blue-600 rounded-lg hover:bg-blue-100 transition-colors" onclick="addMedication()">
                                <i class="fas fa-plus mr-2"></i>添加药物
                            </button>
                        </div>

                        <!-- 实验室检查 -->
                        <div class="form-section glass-card rounded-2xl p-8">
                            <h3 class="text-xl font-semibold text-gray-800 mb-6 flex items-center">
                                <i class="fas fa-vial text-purple-600 mr-3"></i>
                                实验室检查
                            </h3>
                            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                                <div class="input-group">
                                    <input type="number" step="0.01" class="smart-input w-full px-4 py-3 rounded-xl" placeholder=" ">
                                    <label class="input-label">总胆固醇 (mmol/L)</label>
                                </div>
                                <div class="input-group">
                                    <input type="number" step="0.01" class="smart-input w-full px-4 py-3 rounded-xl" placeholder=" ">
                                    <label class="input-label">低密度脂蛋白 (mmol/L)</label>
                                </div>
                                <div class="input-group">
                                    <input type="number" step="0.01" class="smart-input w-full px-4 py-3 rounded-xl" placeholder=" ">
                                    <label class="input-label">高密度脂蛋白 (mmol/L)</label>
                                </div>
                                <div class="input-group">
                                    <input type="number" step="0.01" class="smart-input w-full px-4 py-3 rounded-xl" placeholder=" ">
                                    <label class="input-label">甘油三酯 (mmol/L)</label>
                                </div>
                                <div class="input-group">
                                    <input type="number" step="0.1" class="smart-input w-full px-4 py-3 rounded-xl" placeholder=" ">
                                    <label class="input-label">空腹血糖 (mmol/L)</label>
                                </div>
                                <div class="input-group">
                                    <input type="number" step="0.1" class="smart-input w-full px-4 py-3 rounded-xl" placeholder=" ">
                                    <label class="input-label">糖化血红蛋白 (%)</label>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <script>
        // 智能验证功能
        const validationRules = {
            systolic: { min: 80, max: 200, normal: [90, 140] },
            diastolic: { min: 50, max: 120, normal: [60, 90] },
            heartRate: { min: 40, max: 150, normal: [60, 100] },
            temperature: { min: 35, max: 42, normal: [36, 37.5] }
        };

        function validateInput(inputId, value) {
            const rule = validationRules[inputId];
            const input = document.getElementById(inputId);
            const message = document.getElementById(inputId + '-msg');
            
            if (!rule || !value) return;
            
            const numValue = parseFloat(value);
            
            if (numValue < rule.min || numValue > rule.max) {
                input.classList.add('invalid');
                input.classList.remove('valid');
                message.textContent = `值应在 ${rule.min}-${rule.max} 范围内`;
                message.className = 'validation-message error';
            } else if (numValue < rule.normal[0] || numValue > rule.normal[1]) {
                input.classList.add('valid');
                input.classList.remove('invalid');
                message.textContent = '值偏离正常范围，请确认';
                message.className = 'validation-message error';
            } else {
                input.classList.add('valid');
                input.classList.remove('invalid');
                message.textContent = '正常范围';
                message.className = 'validation-message success';
            }
        }

        // 为生命体征输入框添加验证
        Object.keys(validationRules).forEach(inputId => {
            const input = document.getElementById(inputId);
            if (input) {
                input.addEventListener('input', function() {
                    validateInput(inputId, this.value);
                });
            }
        });

        // 药物自动完成功能
        document.getElementById('medication1').addEventListener('input', function() {
            const value = this.value.toLowerCase();
            const complete = document.getElementById('medication1-complete');
            
            if (value.length > 0) {
                complete.classList.remove('hidden');
            } else {
                complete.classList.add('hidden');
            }
        });

        // 点击自动完成项目
        document.querySelectorAll('.auto-complete-item').forEach(item => {
            item.addEventListener('click', function() {
                const input = this.closest('.input-group').querySelector('input');
                input.value = this.textContent;
                this.closest('.auto-complete').classList.add('hidden');
            });
        });

        // 添加药物功能
        function addMedication() {
            const medicationList = document.getElementById('medicationList');
            const newMedication = document.createElement('div');
            newMedication.className = 'medication-item border border-gray-200 rounded-xl p-4';
            newMedication.innerHTML = `
                <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                    <div class="input-group">
                        <input type="text" class="smart-input w-full px-4 py-3 rounded-xl" placeholder=" ">
                        <label class="input-label">药物名称</label>
                    </div>
                    <div class="input-group">
                        <input type="text" class="smart-input w-full px-4 py-3 rounded-xl" placeholder=" ">
                        <label class="input-label">剂量</label>
                    </div>
                    <div class="input-group">
                        <select class="smart-input w-full px-4 py-3 rounded-xl">
                            <option value="">请选择</option>
                            <option value="qd">每日一次</option>
                            <option value="bid">每日两次</option>
                            <option value="tid">每日三次</option>
                            <option value="qid">每日四次</option>
                        </select>
                        <label class="input-label">用药频次</label>
                    </div>
                    <div class="flex items-center">
                        <button class="text-red-500 hover:text-red-700 p-2" onclick="removeMedication(this)">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </div>
            `;
            medicationList.appendChild(newMedication);
        }

        function removeMedication(button) {
            button.closest('.medication-item').remove();
        }

        // 自动保存功能
        let autoSaveTimer;
        document.querySelectorAll('input, select, textarea').forEach(input => {
            input.addEventListener('input', function() {
                clearTimeout(autoSaveTimer);
                autoSaveTimer = setTimeout(() => {
                    console.log('自动保存数据...');
                    // 这里可以添加实际的保存逻辑
                }, 2000);
            });
        });

        // 智能填充建议
        document.querySelector('.quick-action:nth-child(2)').addEventListener('click', function() {
            // 模拟智能填充
            document.getElementById('systolic').value = '120';
            document.getElementById('diastolic').value = '80';
            document.getElementById('heartRate').value = '72';
            document.getElementById('temperature').value = '36.5';
            
            // 触发验证
            ['systolic', 'diastolic', 'heartRate', 'temperature'].forEach(id => {
                const input = document.getElementById(id);
                validateInput(id, input.value);
            });
        });
    </script>
</body>
</html>
