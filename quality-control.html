<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>质量控制 - 医疗CRF编辑器</title>
    <script src="https://cdn.jsdelivr.net/npm/@unocss/runtime"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
        
        body {
            font-family: 'Inter', sans-serif;
            background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
            min-height: 100vh;
        }
        
        .glass-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }
        
        .metric-card {
            transition: all 0.3s ease;
            cursor: pointer;
        }
        
        .metric-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 12px 24px rgba(0, 0, 0, 0.15);
        }
        
        .issue-item {
            transition: all 0.3s ease;
            border-left: 4px solid transparent;
        }
        
        .issue-item:hover {
            background: #f9fafb;
            border-left-color: #3b82f6;
        }
        
        .issue-high { border-left-color: #ef4444; }
        .issue-medium { border-left-color: #f59e0b; }
        .issue-low { border-left-color: #10b981; }
        
        .priority-badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 500;
        }
        
        .priority-high { background: #fef2f2; color: #dc2626; }
        .priority-medium { background: #fffbeb; color: #d97706; }
        .priority-low { background: #f0fdf4; color: #16a34a; }
        
        .status-badge {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 500;
        }
        
        .status-pending { background: #fef3c7; color: #92400e; }
        .status-reviewing { background: #dbeafe; color: #1e40af; }
        .status-resolved { background: #dcfce7; color: #166534; }
        
        .chart-container {
            position: relative;
            height: 300px;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
            border: none;
            transition: all 0.3s ease;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 16px rgba(245, 158, 11, 0.3);
        }
        
        .filter-tab {
            padding: 8px 16px;
            border-radius: 20px;
            transition: all 0.3s ease;
            cursor: pointer;
        }
        
        .filter-tab.active {
            background: #3b82f6;
            color: white;
        }
        
        .filter-tab:not(.active) {
            background: #f3f4f6;
            color: #6b7280;
        }
        
        .filter-tab:not(.active):hover {
            background: #e5e7eb;
        }
        
        .audit-trail {
            border-left: 2px solid #e5e7eb;
            position: relative;
        }
        
        .audit-item {
            position: relative;
            padding-left: 24px;
        }
        
        .audit-item::before {
            content: '';
            position: absolute;
            left: -5px;
            top: 8px;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #3b82f6;
        }
        
        .data-quality-score {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            color: white;
            border-radius: 50%;
            width: 120px;
            height: 120px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            margin: 0 auto;
        }
    </style>
</head>
<body class="m-0 p-0">
    <!-- 导航栏 -->
    <nav class="glass-card fixed top-0 left-0 right-0 z-50 px-6 py-4">
        <div class="flex items-center justify-between max-w-7xl mx-auto">
            <div class="flex items-center space-x-3">
                <i class="fas fa-heartbeat text-3xl text-blue-600"></i>
                <h1 class="text-2xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">MediCRF Pro</h1>
            </div>
            <div class="hidden md:flex items-center space-x-8">
                <a href="index.html" class="text-gray-700 hover:text-blue-600 font-medium transition-colors">首页</a>
                <a href="projects.html" class="text-gray-700 hover:text-blue-600 font-medium transition-colors">项目管理</a>
                <a href="form-designer.html" class="text-gray-700 hover:text-blue-600 font-medium transition-colors">表单设计</a>
                <a href="data-entry.html" class="text-gray-700 hover:text-blue-600 font-medium transition-colors">数据录入</a>
                <a href="#" class="text-blue-600 font-medium">质量控制</a>
            </div>
            <div class="flex items-center space-x-4">
                <button class="p-2 rounded-full hover:bg-gray-100 transition-colors relative">
                    <i class="fas fa-bell text-gray-600"></i>
                    <span class="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">3</span>
                </button>
                <div class="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center">
                    <i class="fas fa-user text-white text-sm"></i>
                </div>
            </div>
        </div>
    </nav>

    <!-- 主内容区域 -->
    <main class="pt-24 pb-12 px-6">
        <div class="max-w-7xl mx-auto">
            <!-- 页面标题 -->
            <div class="mb-8">
                <h2 class="text-3xl font-bold text-gray-800 mb-2">质量控制中心</h2>
                <p class="text-gray-600">监控数据质量，确保研究合规性</p>
            </div>

            <!-- 质量指标概览 -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                <div class="metric-card glass-card rounded-2xl p-6 text-center">
                    <div class="data-quality-score mx-auto mb-4">
                        <div class="text-2xl font-bold">92%</div>
                        <div class="text-sm">数据质量</div>
                    </div>
                </div>
                
                <div class="metric-card glass-card rounded-2xl p-6">
                    <div class="flex items-center justify-between mb-4">
                        <div class="w-12 h-12 bg-red-100 rounded-xl flex items-center justify-center">
                            <i class="fas fa-exclamation-triangle text-red-600 text-xl"></i>
                        </div>
                        <span class="text-2xl font-bold text-red-600">23</span>
                    </div>
                    <h3 class="font-semibold text-gray-800 mb-1">待处理问题</h3>
                    <p class="text-sm text-gray-600">需要立即关注</p>
                </div>

                <div class="metric-card glass-card rounded-2xl p-6">
                    <div class="flex items-center justify-between mb-4">
                        <div class="w-12 h-12 bg-blue-100 rounded-xl flex items-center justify-center">
                            <i class="fas fa-check-circle text-blue-600 text-xl"></i>
                        </div>
                        <span class="text-2xl font-bold text-blue-600">156</span>
                    </div>
                    <h3 class="font-semibold text-gray-800 mb-1">已审核记录</h3>
                    <p class="text-sm text-gray-600">本周完成</p>
                </div>

                <div class="metric-card glass-card rounded-2xl p-6">
                    <div class="flex items-center justify-between mb-4">
                        <div class="w-12 h-12 bg-green-100 rounded-xl flex items-center justify-center">
                            <i class="fas fa-shield-alt text-green-600 text-xl"></i>
                        </div>
                        <span class="text-2xl font-bold text-green-600">98.5%</span>
                    </div>
                    <h3 class="font-semibold text-gray-800 mb-1">合规率</h3>
                    <p class="text-sm text-gray-600">符合GCP标准</p>
                </div>
            </div>

            <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
                <!-- 左侧问题列表 -->
                <div class="lg:col-span-2">
                    <div class="glass-card rounded-2xl p-6 mb-8">
                        <div class="flex items-center justify-between mb-6">
                            <h3 class="text-xl font-semibold text-gray-800">数据质量问题</h3>
                            <div class="flex space-x-2">
                                <span class="filter-tab active">全部</span>
                                <span class="filter-tab">高优先级</span>
                                <span class="filter-tab">待处理</span>
                                <span class="filter-tab">已解决</span>
                            </div>
                        </div>

                        <div class="space-y-4">
                            <div class="issue-item issue-high p-4 rounded-lg border border-gray-200">
                                <div class="flex items-start justify-between mb-3">
                                    <div class="flex items-center space-x-3">
                                        <span class="priority-badge priority-high">高</span>
                                        <h4 class="font-medium text-gray-800">生命体征数据异常</h4>
                                    </div>
                                    <span class="status-badge status-pending">待处理</span>
                                </div>
                                <p class="text-sm text-gray-600 mb-3">患者P001234的血压值超出正常范围（收缩压: 200mmHg），需要医生确认</p>
                                <div class="flex items-center justify-between text-xs text-gray-500">
                                    <span>患者ID: P001234 | 表单: 基线访问</span>
                                    <span>2024-03-20 14:30</span>
                                </div>
                            </div>

                            <div class="issue-item issue-medium p-4 rounded-lg border border-gray-200">
                                <div class="flex items-start justify-between mb-3">
                                    <div class="flex items-center space-x-3">
                                        <span class="priority-badge priority-medium">中</span>
                                        <h4 class="font-medium text-gray-800">必填字段缺失</h4>
                                    </div>
                                    <span class="status-badge status-reviewing">审核中</span>
                                </div>
                                <p class="text-sm text-gray-600 mb-3">患者P001235的用药记录中缺少药物剂量信息</p>
                                <div class="flex items-center justify-between text-xs text-gray-500">
                                    <span>患者ID: P001235 | 表单: 随访1</span>
                                    <span>2024-03-20 11:15</span>
                                </div>
                            </div>

                            <div class="issue-item issue-low p-4 rounded-lg border border-gray-200">
                                <div class="flex items-start justify-between mb-3">
                                    <div class="flex items-center space-x-3">
                                        <span class="priority-badge priority-low">低</span>
                                        <h4 class="font-medium text-gray-800">数据格式不一致</h4>
                                    </div>
                                    <span class="status-badge status-resolved">已解决</span>
                                </div>
                                <p class="text-sm text-gray-600 mb-3">日期格式不统一，已自动修正为标准格式</p>
                                <div class="flex items-center justify-between text-xs text-gray-500">
                                    <span>患者ID: P001236 | 表单: 基线访问</span>
                                    <span>2024-03-19 16:45</span>
                                </div>
                            </div>

                            <div class="issue-item issue-medium p-4 rounded-lg border border-gray-200">
                                <div class="flex items-start justify-between mb-3">
                                    <div class="flex items-center space-x-3">
                                        <span class="priority-badge priority-medium">中</span>
                                        <h4 class="font-medium text-gray-800">逻辑一致性检查</h4>
                                    </div>
                                    <span class="status-badge status-pending">待处理</span>
                                </div>
                                <p class="text-sm text-gray-600 mb-3">患者年龄与出生日期不匹配，请核实</p>
                                <div class="flex items-center justify-between text-xs text-gray-500">
                                    <span>患者ID: P001237 | 表单: 基线访问</span>
                                    <span>2024-03-20 09:20</span>
                                </div>
                            </div>
                        </div>

                        <div class="mt-6 flex justify-center">
                            <button class="btn-primary text-white px-6 py-3 rounded-xl font-semibold">
                                查看更多问题
                            </button>
                        </div>
                    </div>

                    <!-- 数据趋势图 -->
                    <div class="glass-card rounded-2xl p-6">
                        <h3 class="text-xl font-semibold text-gray-800 mb-6">数据质量趋势</h3>
                        <div class="chart-container bg-gray-50 rounded-xl flex items-center justify-center">
                            <div class="text-center text-gray-500">
                                <i class="fas fa-chart-line text-4xl mb-4"></i>
                                <p>数据质量趋势图</p>
                                <p class="text-sm">显示过去30天的质量指标变化</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 右侧审核面板 -->
                <div class="lg:col-span-1">
                    <!-- 快速操作 -->
                    <div class="glass-card rounded-2xl p-6 mb-6">
                        <h4 class="font-semibold text-gray-800 mb-4">快速操作</h4>
                        <div class="space-y-3">
                            <button class="w-full p-3 bg-blue-50 text-blue-700 rounded-lg hover:bg-blue-100 transition-colors text-left">
                                <i class="fas fa-search mr-2"></i>
                                数据完整性检查
                            </button>
                            <button class="w-full p-3 bg-green-50 text-green-700 rounded-lg hover:bg-green-100 transition-colors text-left">
                                <i class="fas fa-shield-alt mr-2"></i>
                                合规性验证
                            </button>
                            <button class="w-full p-3 bg-purple-50 text-purple-700 rounded-lg hover:bg-purple-100 transition-colors text-left">
                                <i class="fas fa-file-export mr-2"></i>
                                生成质量报告
                            </button>
                            <button class="w-full p-3 bg-orange-50 text-orange-700 rounded-lg hover:bg-orange-100 transition-colors text-left">
                                <i class="fas fa-cog mr-2"></i>
                                配置检查规则
                            </button>
                        </div>
                    </div>

                    <!-- 审核历史 -->
                    <div class="glass-card rounded-2xl p-6 mb-6">
                        <h4 class="font-semibold text-gray-800 mb-4">最近审核</h4>
                        <div class="audit-trail space-y-4">
                            <div class="audit-item">
                                <div class="text-sm font-medium text-gray-800">张医生</div>
                                <div class="text-xs text-gray-600">审核了患者P001234的生命体征数据</div>
                                <div class="text-xs text-gray-400">2小时前</div>
                            </div>
                            <div class="audit-item">
                                <div class="text-sm font-medium text-gray-800">李医生</div>
                                <div class="text-xs text-gray-600">解决了数据格式不一致问题</div>
                                <div class="text-xs text-gray-400">4小时前</div>
                            </div>
                            <div class="audit-item">
                                <div class="text-sm font-medium text-gray-800">王医生</div>
                                <div class="text-xs text-gray-600">完成了批量数据验证</div>
                                <div class="text-xs text-gray-400">1天前</div>
                            </div>
                        </div>
                    </div>

                    <!-- 统计信息 -->
                    <div class="glass-card rounded-2xl p-6">
                        <h4 class="font-semibold text-gray-800 mb-4">本周统计</h4>
                        <div class="space-y-4">
                            <div class="flex justify-between items-center">
                                <span class="text-sm text-gray-600">新增问题</span>
                                <span class="font-semibold text-red-600">12</span>
                            </div>
                            <div class="flex justify-between items-center">
                                <span class="text-sm text-gray-600">已解决问题</span>
                                <span class="font-semibold text-green-600">28</span>
                            </div>
                            <div class="flex justify-between items-center">
                                <span class="text-sm text-gray-600">审核记录数</span>
                                <span class="font-semibold text-blue-600">156</span>
                            </div>
                            <div class="flex justify-between items-center">
                                <span class="text-sm text-gray-600">平均处理时间</span>
                                <span class="font-semibold text-purple-600">2.3小时</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <script>
        // 筛选标签功能
        document.querySelectorAll('.filter-tab').forEach(tab => {
            tab.addEventListener('click', function() {
                // 移除所有active类
                document.querySelectorAll('.filter-tab').forEach(t => t.classList.remove('active'));
                // 添加active类到当前标签
                this.classList.add('active');
                
                // 这里可以添加筛选逻辑
                console.log('筛选:', this.textContent);
            });
        });

        // 问题项点击效果
        document.querySelectorAll('.issue-item').forEach(item => {
            item.addEventListener('click', function() {
                // 添加选中效果
                document.querySelectorAll('.issue-item').forEach(i => i.classList.remove('bg-blue-50'));
                this.classList.add('bg-blue-50');
                
                // 这里可以显示详细信息
                console.log('选中问题:', this.querySelector('h4').textContent);
            });
        });

        // 快速操作按钮
        document.querySelectorAll('.glass-card button').forEach(button => {
            button.addEventListener('click', function() {
                const action = this.textContent.trim();
                console.log('执行操作:', action);
                
                // 添加点击反馈
                this.style.transform = 'scale(0.98)';
                setTimeout(() => {
                    this.style.transform = '';
                }, 150);
            });
        });

        // 模拟实时数据更新
        setInterval(() => {
            // 更新通知数量
            const notificationBadge = document.querySelector('.fa-bell + span');
            if (notificationBadge) {
                const currentCount = parseInt(notificationBadge.textContent);
                if (Math.random() > 0.8) { // 20%概率增加通知
                    notificationBadge.textContent = currentCount + 1;
                }
            }
        }, 10000);
    </script>
</body>
</html>
