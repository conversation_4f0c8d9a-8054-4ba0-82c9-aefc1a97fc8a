<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>项目管理 - 医疗CRF编辑器</title>
    <script src="https://cdn.jsdelivr.net/npm/@unocss/runtime"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

        body {
            font-family: 'Inter', sans-serif;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
        }

        .glass-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

        .project-card {
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .project-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 12px 24px rgba(0, 0, 0, 0.15);
        }

        .status-badge {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 500;
        }

        .status-active { background: #dcfce7; color: #166534; }
        .status-review { background: #dbeafe; color: #1e40af; }
        .status-completed { background: #f3e8ff; color: #7c3aed; }
        .status-draft { background: #fef3c7; color: #92400e; }

        .progress-bar {
            height: 6px;
            background: #e5e7eb;
            border-radius: 3px;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #10b981, #059669);
            transition: width 0.3s ease;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            transition: all 0.3s ease;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 16px rgba(102, 126, 234, 0.3);
        }

        .search-box {
            background: rgba(255, 255, 255, 0.9);
            border: 1px solid rgba(255, 255, 255, 0.3);
            backdrop-filter: blur(10px);
        }
    </style>
</head>
<body class="m-0 p-0">
    <!-- 导航栏 -->
    <nav class="glass-card fixed top-0 left-0 right-0 z-50 px-6 py-4">
        <div class="flex items-center justify-between max-w-7xl mx-auto">
            <div class="flex items-center space-x-3">
                <i class="fas fa-heartbeat text-3xl text-blue-600"></i>
                <h1 class="text-2xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">MediCRF Pro</h1>
            </div>
            <div class="hidden md:flex items-center space-x-8">
                <a href="index.html" class="text-gray-700 hover:text-blue-600 font-medium transition-colors">首页</a>
                <a href="#" class="text-blue-600 font-medium">项目管理</a>
                <a href="form-designer.html" class="text-gray-700 hover:text-blue-600 font-medium transition-colors">表单设计</a>
                <a href="data-entry.html" class="text-gray-700 hover:text-blue-600 font-medium transition-colors">数据录入</a>
                <a href="quality-control.html" class="text-gray-700 hover:text-blue-600 font-medium transition-colors">质量控制</a>
            </div>
            <div class="flex items-center space-x-4">
                <button class="p-2 rounded-full hover:bg-gray-100 transition-colors">
                    <i class="fas fa-bell text-gray-600"></i>
                </button>
                <div class="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center">
                    <i class="fas fa-user text-white text-sm"></i>
                </div>
            </div>
        </div>
    </nav>

    <!-- 主内容区域 -->
    <main class="pt-24 pb-12 px-6">
        <div class="max-w-7xl mx-auto">
            <!-- 页面标题和操作 -->
            <div class="flex items-center justify-between mb-8">
                <div>
                    <h2 class="text-3xl font-bold text-gray-800 mb-2">项目管理</h2>
                    <p class="text-gray-600">管理您的临床研究项目</p>
                </div>
                <button class="btn-primary text-white px-6 py-3 rounded-xl font-semibold flex items-center space-x-2">
                    <i class="fas fa-plus"></i>
                    <span>新建项目</span>
                </button>
            </div>

            <!-- 搜索和筛选 -->
            <div class="glass-card rounded-2xl p-6 mb-8">
                <div class="flex flex-col md:flex-row gap-4">
                    <div class="flex-1">
                        <div class="relative">
                            <i class="fas fa-search absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
                            <input type="text" placeholder="搜索项目名称、研究者..."
                                   class="search-box w-full pl-12 pr-4 py-3 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500">
                        </div>
                    </div>
                    <div class="flex gap-3">
                        <select class="search-box px-4 py-3 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <option>全部状态</option>
                            <option>进行中</option>
                            <option>审核中</option>
                            <option>已完成</option>
                            <option>草稿</option>
                        </select>
                        <select class="search-box px-4 py-3 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <option>全部类型</option>
                            <option>临床试验</option>
                            <option>观察性研究</option>
                            <option>流行病学调查</option>
                        </select>
                    </div>
                </div>
            </div>

            <!-- 项目统计 -->
            <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
                <div class="glass-card rounded-2xl p-6 text-center">
                    <div class="text-3xl font-bold text-blue-600 mb-2">24</div>
                    <div class="text-gray-600">总项目数</div>
                </div>
                <div class="glass-card rounded-2xl p-6 text-center">
                    <div class="text-3xl font-bold text-green-600 mb-2">18</div>
                    <div class="text-gray-600">进行中</div>
                </div>
                <div class="glass-card rounded-2xl p-6 text-center">
                    <div class="text-3xl font-bold text-purple-600 mb-2">4</div>
                    <div class="text-gray-600">待审核</div>
                </div>
                <div class="glass-card rounded-2xl p-6 text-center">
                    <div class="text-3xl font-bold text-orange-600 mb-2">2</div>
                    <div class="text-gray-600">草稿</div>
                </div>
            </div>

            <!-- 项目列表 -->
            <div class="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
                <!-- 项目卡片 1 -->
                <div class="project-card glass-card rounded-2xl p-6" onclick="location.href='form-designer.html'">
                    <div class="flex items-start justify-between mb-4">
                        <div class="w-12 h-12 bg-blue-100 rounded-xl flex items-center justify-center">
                            <i class="fas fa-heartbeat text-blue-600 text-xl"></i>
                        </div>
                        <span class="status-badge status-active">进行中</span>
                    </div>
                    <h3 class="text-xl font-semibold text-gray-800 mb-2">心血管疾病研究</h3>
                    <p class="text-gray-600 text-sm mb-4 line-clamp-2">
                        针对冠心病患者的长期随访研究，评估新型治疗方案的有效性和安全性
                    </p>
                    <div class="space-y-3">
                        <div class="flex items-center justify-between text-sm">
                            <span class="text-gray-500">进度</span>
                            <span class="font-medium text-gray-700">78%</span>
                        </div>
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: 78%"></div>
                        </div>
                        <div class="flex items-center justify-between text-sm text-gray-500">
                            <span>主研究者：张医生</span>
                            <span>2024-01-15</span>
                        </div>
                    </div>
                </div>

                <!-- 项目卡片 2 -->
                <div class="project-card glass-card rounded-2xl p-6">
                    <div class="flex items-start justify-between mb-4">
                        <div class="w-12 h-12 bg-green-100 rounded-xl flex items-center justify-center">
                            <i class="fas fa-brain text-green-600 text-xl"></i>
                        </div>
                        <span class="status-badge status-review">审核中</span>
                    </div>
                    <h3 class="text-xl font-semibold text-gray-800 mb-2">神经系统疾病调研</h3>
                    <p class="text-gray-600 text-sm mb-4 line-clamp-2">
                        阿尔茨海默病早期诊断标志物的多中心前瞻性队列研究
                    </p>
                    <div class="space-y-3">
                        <div class="flex items-center justify-between text-sm">
                            <span class="text-gray-500">进度</span>
                            <span class="font-medium text-gray-700">92%</span>
                        </div>
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: 92%"></div>
                        </div>
                        <div class="flex items-center justify-between text-sm text-gray-500">
                            <span>主研究者：李医生</span>
                            <span>2024-02-20</span>
                        </div>
                    </div>
                </div>

                <!-- 项目卡片 3 -->
                <div class="project-card glass-card rounded-2xl p-6">
                    <div class="flex items-start justify-between mb-4">
                        <div class="w-12 h-12 bg-purple-100 rounded-xl flex items-center justify-center">
                            <i class="fas fa-lungs text-purple-600 text-xl"></i>
                        </div>
                        <span class="status-badge status-active">进行中</span>
                    </div>
                    <h3 class="text-xl font-semibold text-gray-800 mb-2">呼吸系统疾病研究</h3>
                    <p class="text-gray-600 text-sm mb-4 line-clamp-2">
                        慢性阻塞性肺疾病患者生活质量评估及干预措施效果研究
                    </p>
                    <div class="space-y-3">
                        <div class="flex items-center justify-between text-sm">
                            <span class="text-gray-500">进度</span>
                            <span class="font-medium text-gray-700">45%</span>
                        </div>
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: 45%"></div>
                        </div>
                        <div class="flex items-center justify-between text-sm text-gray-500">
                            <span>主研究者：王医生</span>
                            <span>2024-03-10</span>
                        </div>
                    </div>
                </div>

                <!-- 项目卡片 4 -->
                <div class="project-card glass-card rounded-2xl p-6">
                    <div class="flex items-start justify-between mb-4">
                        <div class="w-12 h-12 bg-red-100 rounded-xl flex items-center justify-center">
                            <i class="fas fa-dna text-red-600 text-xl"></i>
                        </div>
                        <span class="status-badge status-completed">已完成</span>
                    </div>
                    <h3 class="text-xl font-semibold text-gray-800 mb-2">肿瘤基因检测研究</h3>
                    <p class="text-gray-600 text-sm mb-4 line-clamp-2">
                        恶性肿瘤患者基因突变谱分析及个体化治疗方案制定
                    </p>
                    <div class="space-y-3">
                        <div class="flex items-center justify-between text-sm">
                            <span class="text-gray-500">进度</span>
                            <span class="font-medium text-gray-700">100%</span>
                        </div>
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: 100%"></div>
                        </div>
                        <div class="flex items-center justify-between text-sm text-gray-500">
                            <span>主研究者：陈医生</span>
                            <span>2023-12-01</span>
                        </div>
                    </div>
                </div>

                <!-- 项目卡片 5 -->
                <div class="project-card glass-card rounded-2xl p-6">
                    <div class="flex items-start justify-between mb-4">
                        <div class="w-12 h-12 bg-yellow-100 rounded-xl flex items-center justify-center">
                            <i class="fas fa-pills text-yellow-600 text-xl"></i>
                        </div>
                        <span class="status-badge status-draft">草稿</span>
                    </div>
                    <h3 class="text-xl font-semibold text-gray-800 mb-2">药物安全性评估</h3>
                    <p class="text-gray-600 text-sm mb-4 line-clamp-2">
                        新型抗炎药物的临床安全性和有效性多中心随机对照试验
                    </p>
                    <div class="space-y-3">
                        <div class="flex items-center justify-between text-sm">
                            <span class="text-gray-500">进度</span>
                            <span class="font-medium text-gray-700">15%</span>
                        </div>
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: 15%"></div>
                        </div>
                        <div class="flex items-center justify-between text-sm text-gray-500">
                            <span>主研究者：赵医生</span>
                            <span>2024-04-01</span>
                        </div>
                    </div>
                </div>

                <!-- 项目卡片 6 -->
                <div class="project-card glass-card rounded-2xl p-6">
                    <div class="flex items-start justify-between mb-4">
                        <div class="w-12 h-12 bg-indigo-100 rounded-xl flex items-center justify-center">
                            <i class="fas fa-microscope text-indigo-600 text-xl"></i>
                        </div>
                        <span class="status-badge status-active">进行中</span>
                    </div>
                    <h3 class="text-xl font-semibold text-gray-800 mb-2">免疫系统研究</h3>
                    <p class="text-gray-600 text-sm mb-4 line-clamp-2">
                        自身免疫性疾病患者免疫功能评估及治疗反应预测研究
                    </p>
                    <div class="space-y-3">
                        <div class="flex items-center justify-between text-sm">
                            <span class="text-gray-500">进度</span>
                            <span class="font-medium text-gray-700">63%</span>
                        </div>
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: 63%"></div>
                        </div>
                        <div class="flex items-center justify-between text-sm text-gray-500">
                            <span>主研究者：孙医生</span>
                            <span>2024-01-30</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 分页 -->
            <div class="flex items-center justify-center mt-12">
                <div class="flex items-center space-x-2">
                    <button class="px-3 py-2 rounded-lg text-gray-500 hover:bg-white hover:text-gray-700 transition-colors">
                        <i class="fas fa-chevron-left"></i>
                    </button>
                    <button class="px-4 py-2 rounded-lg bg-blue-600 text-white">1</button>
                    <button class="px-4 py-2 rounded-lg text-gray-500 hover:bg-white hover:text-gray-700 transition-colors">2</button>
                    <button class="px-4 py-2 rounded-lg text-gray-500 hover:bg-white hover:text-gray-700 transition-colors">3</button>
                    <button class="px-3 py-2 rounded-lg text-gray-500 hover:bg-white hover:text-gray-700 transition-colors">
                        <i class="fas fa-chevron-right"></i>
                    </button>
                </div>
            </div>
        </div>
    </main>

    <script>
        // 搜索功能
        document.querySelector('input[type="text"]').addEventListener('input', function(e) {
            const searchTerm = e.target.value.toLowerCase();
            const projectCards = document.querySelectorAll('.project-card');

            projectCards.forEach(card => {
                const title = card.querySelector('h3').textContent.toLowerCase();
                const description = card.querySelector('p').textContent.toLowerCase();

                if (title.includes(searchTerm) || description.includes(searchTerm)) {
                    card.style.display = 'block';
                } else {
                    card.style.display = 'none';
                }
            });
        });

        // 筛选功能
        document.querySelectorAll('select').forEach(select => {
            select.addEventListener('change', function() {
                // 这里可以添加筛选逻辑
                console.log('筛选条件改变:', this.value);
            });
        });

        // 项目卡片点击效果
        document.querySelectorAll('.project-card').forEach(card => {
            card.addEventListener('click', function() {
                // 添加点击反馈
                this.style.transform = 'scale(0.98)';
                setTimeout(() => {
                    this.style.transform = '';
                }, 150);
            });
        });
    </script>
</body>
</html>
