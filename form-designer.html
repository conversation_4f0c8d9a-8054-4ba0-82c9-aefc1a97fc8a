<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>表单设计器 - 医疗CRF编辑器</title>
    <script src="https://cdn.jsdelivr.net/npm/@unocss/runtime"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
        
        body {
            font-family: 'Inter', sans-serif;
            background: #f8fafc;
            min-height: 100vh;
        }
        
        .glass-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }
        
        .sidebar {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        
        .component-item {
            transition: all 0.3s ease;
            cursor: grab;
        }
        
        .component-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }
        
        .component-item:active {
            cursor: grabbing;
        }
        
        .form-canvas {
            background: white;
            border: 2px dashed #e2e8f0;
            min-height: 600px;
            transition: all 0.3s ease;
        }
        
        .form-canvas.drag-over {
            border-color: #667eea;
            background: #f0f4ff;
        }
        
        .form-element {
            border: 1px solid #e2e8f0;
            transition: all 0.3s ease;
            position: relative;
        }
        
        .form-element:hover {
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }
        
        .form-element.selected {
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.2);
        }
        
        .element-controls {
            position: absolute;
            top: -12px;
            right: -12px;
            opacity: 0;
            transition: opacity 0.3s ease;
        }
        
        .form-element:hover .element-controls {
            opacity: 1;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            transition: all 0.3s ease;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 16px rgba(102, 126, 234, 0.3);
        }
        
        .property-panel {
            background: white;
            border-left: 1px solid #e2e8f0;
        }
        
        .medical-template {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            color: white;
            transition: all 0.3s ease;
        }
        
        .medical-template:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 16px rgba(16, 185, 129, 0.3);
        }
    </style>
</head>
<body class="m-0 p-0">
    <!-- 导航栏 -->
    <nav class="glass-card fixed top-0 left-0 right-0 z-50 px-6 py-4">
        <div class="flex items-center justify-between max-w-full mx-auto">
            <div class="flex items-center space-x-3">
                <i class="fas fa-heartbeat text-3xl text-blue-600"></i>
                <h1 class="text-2xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">MediCRF Pro</h1>
            </div>
            <div class="hidden md:flex items-center space-x-8">
                <a href="index.html" class="text-gray-700 hover:text-blue-600 font-medium transition-colors">首页</a>
                <a href="projects.html" class="text-gray-700 hover:text-blue-600 font-medium transition-colors">项目管理</a>
                <a href="#" class="text-blue-600 font-medium">表单设计</a>
                <a href="data-entry.html" class="text-gray-700 hover:text-blue-600 font-medium transition-colors">数据录入</a>
                <a href="quality-control.html" class="text-gray-700 hover:text-blue-600 font-medium transition-colors">质量控制</a>
            </div>
            <div class="flex items-center space-x-4">
                <button class="btn-primary text-white px-4 py-2 rounded-lg font-medium">
                    <i class="fas fa-save mr-2"></i>保存
                </button>
                <button class="btn-primary text-white px-4 py-2 rounded-lg font-medium">
                    <i class="fas fa-eye mr-2"></i>预览
                </button>
                <div class="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center">
                    <i class="fas fa-user text-white text-sm"></i>
                </div>
            </div>
        </div>
    </nav>

    <!-- 主内容区域 -->
    <div class="pt-20 h-screen flex">
        <!-- 左侧组件面板 -->
        <div class="sidebar w-80 p-6 overflow-y-auto">
            <h3 class="text-xl font-semibold mb-6">组件库</h3>
            
            <!-- 医疗专业模板 -->
            <div class="mb-8">
                <h4 class="text-lg font-medium mb-4 text-white/90">医疗专业模板</h4>
                <div class="space-y-3">
                    <div class="medical-template p-4 rounded-xl cursor-pointer">
                        <div class="flex items-center space-x-3">
                            <i class="fas fa-heartbeat text-xl"></i>
                            <div>
                                <div class="font-medium">生命体征表</div>
                                <div class="text-sm text-white/80">血压、心率、体温等</div>
                            </div>
                        </div>
                    </div>
                    <div class="medical-template p-4 rounded-xl cursor-pointer">
                        <div class="flex items-center space-x-3">
                            <i class="fas fa-pills text-xl"></i>
                            <div>
                                <div class="font-medium">用药记录表</div>
                                <div class="text-sm text-white/80">药物名称、剂量、频次</div>
                            </div>
                        </div>
                    </div>
                    <div class="medical-template p-4 rounded-xl cursor-pointer">
                        <div class="flex items-center space-x-3">
                            <i class="fas fa-vial text-xl"></i>
                            <div>
                                <div class="font-medium">实验室检查</div>
                                <div class="text-sm text-white/80">血常规、生化指标</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 基础组件 -->
            <div class="mb-8">
                <h4 class="text-lg font-medium mb-4 text-white/90">基础组件</h4>
                <div class="grid grid-cols-2 gap-3">
                    <div class="component-item bg-white/20 p-4 rounded-xl text-center" draggable="true" data-type="text">
                        <i class="fas fa-font text-2xl mb-2"></i>
                        <div class="text-sm">文本输入</div>
                    </div>
                    <div class="component-item bg-white/20 p-4 rounded-xl text-center" draggable="true" data-type="number">
                        <i class="fas fa-hashtag text-2xl mb-2"></i>
                        <div class="text-sm">数字输入</div>
                    </div>
                    <div class="component-item bg-white/20 p-4 rounded-xl text-center" draggable="true" data-type="select">
                        <i class="fas fa-list text-2xl mb-2"></i>
                        <div class="text-sm">下拉选择</div>
                    </div>
                    <div class="component-item bg-white/20 p-4 rounded-xl text-center" draggable="true" data-type="radio">
                        <i class="fas fa-dot-circle text-2xl mb-2"></i>
                        <div class="text-sm">单选按钮</div>
                    </div>
                    <div class="component-item bg-white/20 p-4 rounded-xl text-center" draggable="true" data-type="checkbox">
                        <i class="fas fa-check-square text-2xl mb-2"></i>
                        <div class="text-sm">复选框</div>
                    </div>
                    <div class="component-item bg-white/20 p-4 rounded-xl text-center" draggable="true" data-type="date">
                        <i class="fas fa-calendar text-2xl mb-2"></i>
                        <div class="text-sm">日期选择</div>
                    </div>
                    <div class="component-item bg-white/20 p-4 rounded-xl text-center" draggable="true" data-type="textarea">
                        <i class="fas fa-align-left text-2xl mb-2"></i>
                        <div class="text-sm">多行文本</div>
                    </div>
                    <div class="component-item bg-white/20 p-4 rounded-xl text-center" draggable="true" data-type="file">
                        <i class="fas fa-upload text-2xl mb-2"></i>
                        <div class="text-sm">文件上传</div>
                    </div>
                </div>
            </div>

            <!-- 医疗专用组件 -->
            <div>
                <h4 class="text-lg font-medium mb-4 text-white/90">医疗专用组件</h4>
                <div class="grid grid-cols-2 gap-3">
                    <div class="component-item bg-white/20 p-4 rounded-xl text-center" draggable="true" data-type="vital-signs">
                        <i class="fas fa-heartbeat text-2xl mb-2"></i>
                        <div class="text-sm">生命体征</div>
                    </div>
                    <div class="component-item bg-white/20 p-4 rounded-xl text-center" draggable="true" data-type="medication">
                        <i class="fas fa-pills text-2xl mb-2"></i>
                        <div class="text-sm">用药信息</div>
                    </div>
                    <div class="component-item bg-white/20 p-4 rounded-xl text-center" draggable="true" data-type="diagnosis">
                        <i class="fas fa-stethoscope text-2xl mb-2"></i>
                        <div class="text-sm">诊断编码</div>
                    </div>
                    <div class="component-item bg-white/20 p-4 rounded-xl text-center" draggable="true" data-type="lab-result">
                        <i class="fas fa-vial text-2xl mb-2"></i>
                        <div class="text-sm">检验结果</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 中间表单画布 -->
        <div class="flex-1 p-6">
            <div class="mb-4 flex items-center justify-between">
                <h3 class="text-xl font-semibold text-gray-800">表单设计画布</h3>
                <div class="flex items-center space-x-3">
                    <button class="px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors">
                        <i class="fas fa-undo mr-2"></i>撤销
                    </button>
                    <button class="px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors">
                        <i class="fas fa-redo mr-2"></i>重做
                    </button>
                </div>
            </div>
            
            <div class="form-canvas rounded-2xl p-8" id="formCanvas">
                <div class="text-center text-gray-400 py-20">
                    <i class="fas fa-mouse-pointer text-4xl mb-4"></i>
                    <p class="text-lg">拖拽组件到这里开始设计表单</p>
                    <p class="text-sm">或选择左侧的医疗专业模板快速开始</p>
                </div>
            </div>
        </div>

        <!-- 右侧属性面板 -->
        <div class="property-panel w-80 p-6 overflow-y-auto">
            <h3 class="text-xl font-semibold mb-6 text-gray-800">属性设置</h3>
            
            <div id="propertyContent" class="text-center text-gray-400 py-20">
                <i class="fas fa-cog text-4xl mb-4"></i>
                <p>选择一个组件来编辑属性</p>
            </div>
        </div>
    </div>

    <script>
        let selectedElement = null;
        let elementCounter = 0;

        // 拖拽功能
        document.querySelectorAll('.component-item').forEach(item => {
            item.addEventListener('dragstart', function(e) {
                e.dataTransfer.setData('text/plain', this.dataset.type);
            });
        });

        const canvas = document.getElementById('formCanvas');
        
        canvas.addEventListener('dragover', function(e) {
            e.preventDefault();
            this.classList.add('drag-over');
        });

        canvas.addEventListener('dragleave', function(e) {
            this.classList.remove('drag-over');
        });

        canvas.addEventListener('drop', function(e) {
            e.preventDefault();
            this.classList.remove('drag-over');
            
            const componentType = e.dataTransfer.getData('text/plain');
            createFormElement(componentType);
        });

        function createFormElement(type) {
            elementCounter++;
            const element = document.createElement('div');
            element.className = 'form-element p-4 mb-4 rounded-lg cursor-pointer';
            element.dataset.type = type;
            element.dataset.id = `element_${elementCounter}`;
            
            let content = '';
            switch(type) {
                case 'text':
                    content = `
                        <label class="block text-sm font-medium text-gray-700 mb-2">文本输入字段</label>
                        <input type="text" class="w-full px-3 py-2 border border-gray-300 rounded-lg" placeholder="请输入文本">
                    `;
                    break;
                case 'number':
                    content = `
                        <label class="block text-sm font-medium text-gray-700 mb-2">数字输入字段</label>
                        <input type="number" class="w-full px-3 py-2 border border-gray-300 rounded-lg" placeholder="请输入数字">
                    `;
                    break;
                case 'vital-signs':
                    content = `
                        <label class="block text-sm font-medium text-gray-700 mb-2">生命体征</label>
                        <div class="grid grid-cols-2 gap-4">
                            <div>
                                <label class="text-xs text-gray-500">收缩压 (mmHg)</label>
                                <input type="number" class="w-full px-3 py-2 border border-gray-300 rounded-lg" placeholder="120">
                            </div>
                            <div>
                                <label class="text-xs text-gray-500">舒张压 (mmHg)</label>
                                <input type="number" class="w-full px-3 py-2 border border-gray-300 rounded-lg" placeholder="80">
                            </div>
                            <div>
                                <label class="text-xs text-gray-500">心率 (次/分)</label>
                                <input type="number" class="w-full px-3 py-2 border border-gray-300 rounded-lg" placeholder="72">
                            </div>
                            <div>
                                <label class="text-xs text-gray-500">体温 (°C)</label>
                                <input type="number" step="0.1" class="w-full px-3 py-2 border border-gray-300 rounded-lg" placeholder="36.5">
                            </div>
                        </div>
                    `;
                    break;
                default:
                    content = `<div class="text-gray-600">组件类型: ${type}</div>`;
            }
            
            element.innerHTML = content + `
                <div class="element-controls">
                    <button class="bg-red-500 text-white p-1 rounded-full w-6 h-6 flex items-center justify-center text-xs" onclick="deleteElement(this)">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            `;
            
            element.addEventListener('click', function() {
                selectElement(this);
            });
            
            // 如果画布是空的，先清空提示内容
            if (canvas.querySelector('.text-center')) {
                canvas.innerHTML = '';
            }
            
            canvas.appendChild(element);
            selectElement(element);
        }

        function selectElement(element) {
            // 移除之前选中的元素的样式
            document.querySelectorAll('.form-element.selected').forEach(el => {
                el.classList.remove('selected');
            });
            
            // 选中当前元素
            element.classList.add('selected');
            selectedElement = element;
            
            // 显示属性面板
            showProperties(element);
        }

        function showProperties(element) {
            const type = element.dataset.type;
            const propertyContent = document.getElementById('propertyContent');
            
            let propertiesHTML = `
                <div class="text-left">
                    <h4 class="font-semibold text-gray-800 mb-4">组件属性</h4>
                    <div class="space-y-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">标签文本</label>
                            <input type="text" class="w-full px-3 py-2 border border-gray-300 rounded-lg" value="字段标签">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">字段名称</label>
                            <input type="text" class="w-full px-3 py-2 border border-gray-300 rounded-lg" value="field_name">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">占位符</label>
                            <input type="text" class="w-full px-3 py-2 border border-gray-300 rounded-lg" value="请输入...">
                        </div>
                        <div class="flex items-center">
                            <input type="checkbox" class="mr-2">
                            <label class="text-sm text-gray-700">必填字段</label>
                        </div>
                        <div class="flex items-center">
                            <input type="checkbox" class="mr-2">
                            <label class="text-sm text-gray-700">启用验证</label>
                        </div>
                    </div>
                </div>
            `;
            
            propertyContent.innerHTML = propertiesHTML;
        }

        function deleteElement(button) {
            const element = button.closest('.form-element');
            element.remove();
            
            // 如果删除后画布为空，显示提示
            if (canvas.children.length === 0) {
                canvas.innerHTML = `
                    <div class="text-center text-gray-400 py-20">
                        <i class="fas fa-mouse-pointer text-4xl mb-4"></i>
                        <p class="text-lg">拖拽组件到这里开始设计表单</p>
                        <p class="text-sm">或选择左侧的医疗专业模板快速开始</p>
                    </div>
                `;
            }
            
            // 清空属性面板
            document.getElementById('propertyContent').innerHTML = `
                <div class="text-center text-gray-400 py-20">
                    <i class="fas fa-cog text-4xl mb-4"></i>
                    <p>选择一个组件来编辑属性</p>
                </div>
            `;
        }

        // 医疗模板点击事件
        document.querySelectorAll('.medical-template').forEach(template => {
            template.addEventListener('click', function() {
                // 清空画布
                canvas.innerHTML = '';
                
                // 根据模板类型创建相应的表单结构
                const templateType = this.querySelector('.font-medium').textContent;
                
                if (templateType === '生命体征表') {
                    createFormElement('vital-signs');
                } else if (templateType === '用药记录表') {
                    createFormElement('medication');
                } else if (templateType === '实验室检查') {
                    createFormElement('lab-result');
                }
            });
        });
    </script>
</body>
</html>
